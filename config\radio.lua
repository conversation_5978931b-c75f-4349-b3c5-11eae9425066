Config.Multifrec = {
    ["LSSD治安局"] = {
        "巡逻组-01",
        "巡逻组-02",
        "巡逻组-03",
        "巡逻组-04",
        "巡逻组-05",
        "待命分配-10",
        "战术-1",
        "战术-2",
        "战术-3",
        "休息室-10",
        "休息室-20",
    },
    ["LSPD警察局"] = {
        "巡逻组-1",
        "巡逻组-2",
        "巡逻组-3",
        "巡逻组-4",
        "巡逻组-5",
        "缉毒组-1",
        "缉毒组-2",
        "缉毒组-3",
        "特战组-1",
        "特战组-2",
        "K9单位",
        "休息室-1",
        "休息室-2",
    },
    ["司法部"] = {
        "中央指挥",
        "巡逻部门",
        "战术-01",
        "战术-02",
        "战术-03",
        "战术-04",
        "巡逻组-10",
        "IAA办公室",
        "调查办公室",
    },
    ["特别单位"] = {
        "SAFD指挥部",
        "消防单位一号",
        "消防单位二号",
        "消防单位三号",
        "急救单位一号",
        "急救单位二号",
        "急救单位三号",
        "医院",
        "消防站"
    },
}


Config.ButtonsMultiFreq = {
    ["BROADCAST SAFD"] = {
        "EMS UNITS"
    },
    ["BROADCAST SAPD"] = {
        "SPECIAL UNITS", "NORTH UNITS", "SOUTH UNITS"
    }
}

Config.BindFreqs = {
    ["talk-to-central"] = {"central"},
    ["talk-waiting-assignment"] = {"esperando-asignacion"},
    ["talk-police-station"] = {"comisaria"},
    ["talk-tacs"] = {"tac-01", "tac-02", "tac-03", "tac-04"},
    ["broadcast-special-units"] = {"SPECIAL UNITS"}
}

Config.MegaphoneVoiceDist = 75.0 -- The distance that the voice of the megaphone will be heared

exports("GetPoliceRadioBinds", function()
    return Config.BindFreqs
end)

exports("GetPoliceRadioChannels", function()
    return Config.Multifrec
end)

exports("GetPoliceRadioButtons", function()
    return Config.ButtonsMultiFreq
end)

Config.RequestsTime = 10 -- Minutos

-- DONT TOUCH THIS

function GetCategoryFreqs(category)
    local list = Config.Multifrec[category]
    if not list then
        print("Category not found: " .. category .. " in Config.Multifrec") 
        return {}
    end
    local freqs = {}
    for k, v in pairs(list) do
        table.insert(freqs, v)
    end
    return freqs
end

local nextButtonMultiFreqUpdate = {}
for buttonName, categories in pairs(Config.ButtonsMultiFreq) do 
    for z, category in pairs(categories) do 
        for k, v in pairs(GetCategoryFreqs(category)) do
            if nextButtonMultiFreqUpdate[buttonName] == nil then
                nextButtonMultiFreqUpdate[buttonName] = {}
            end
            nextButtonMultiFreqUpdate[buttonName][#nextButtonMultiFreqUpdate[buttonName] + 1] = v
        end
    end
end

local newBindFreqs = {}
for button, freqs in pairs(Config.BindFreqs) do 
    for _, freq in pairs(freqs) do 
        if Config.Multifrec[freq] then
            for _, freq2 in pairs(Config.Multifrec[freq]) do 
                newBindFreqs[button] = newBindFreqs[button] or {}
                table.insert(newBindFreqs[button], freq2:lower())
            end
        else
            newBindFreqs[button] = newBindFreqs[button] or {}
            table.insert(newBindFreqs[button], freq)
        end
    end
end

for button, freqs in pairs(newBindFreqs) do 
    table.insert(Config.BindFreqs[button], freqs)
end

Config.ButtonsMultiFreq = nextButtonMultiFreqUpdate