RegisterNetEvent("origen_police:client:OnFederalJoin", function()
    -- Event that is triggered when the player joins the federal prison
end)

RegisterNetEvent("origen_police:client:OnFederalLeave", function()
    -- Event that is triggered when the player leaves the federal prison
end)

-- THE ABOVE FUNCTIONS ONLY WORK IF Config.OwnPrisionSystem IS SET TO TRUE IN config/federal.lua

function SpawnInJail(PlayerData)
    -- Your code
end

function setPrision(minutes, danger)
    -- Your code
end