Config.JobCategory = {
    ["police"] = {
        {
            name = "police",
            titleListLabel = "Police List",
            color = "#3f5ba1",
            badge = "lspd_badge", -- only exist lspd_badge and bcsd_badge please don't add other badges name
            penalFilter = "police", -- The name of the penal code filter to separate the penal code by job
            colorHueDeg = 0, -- The hue degree of the color, 0 is the default color, you can change it to get a different color
            society = "police", -- The name of the society that will receive the money when a player pay a bill
        },
        {
            name = "sheriff",
            titleListLabel = "Sheriff List",
            color = "#a1823f",
            badge = "bcsd_badge", -- only exist lspd_badge and bcsd_badge please don't add other badges name
            penalFilter = "police", -- The name of the penal code filter to separate the penal code by job
            colorHueDeg = 0, -- The hue degree of the color, 0 is the default color, you can change it to get a different color
            society = "police", -- The name of the society that will receive the money when a player pay a bill
        },
        
    },

    ["lssd1"] = {
        {
            name = "lssd1",
            titleListLabel = "Police List",
            color = "#3f5ba1",
            badge = "lspd_badge", -- only exist lspd_badge and bcsd_badge please don't add other badges name
            penalFilter = "lssd1", -- The name of the penal code filter to separate the penal code by job
            colorHueDeg = 0, -- The hue degree of the color, 0 is the default color, you can change it to get a different color
            society = "lssd1", -- The name of the society that will receive the money when a player pay a bill
        },
        {
            name = "sheriff",
            titleListLabel = "Sheriff List",
            color = "#a1823f",
            badge = "bcsd_badge", -- only exist lspd_badge and bcsd_badge please don't add other badges name
            penalFilter = "lssd1", -- The name of the penal code filter to separate the penal code by job
            colorHueDeg = 0, -- The hue degree of the color, 0 is the default color, you can change it to get a different color
            society = "lssd1", -- The name of the society that will receive the money when a player pay a bill
        },
        
    },
}

Config.BossGrade = { 4 }  -- ONLY ESX, set the grades that will be considered as boss and will have more permissions like manage penal code

Config.Permissions = {
    [Config.PoliceJobName] = {
        --Tabs
        Dispatch = 0,
        SearchCitizen = 0,
        SearchReports = 0,
        SearchVehicles = 0,
        CriminalCode = 0,
        SearchCapture = 0,
        SearchDebtors = 0,
        FederalManagement = 0,
        AgentManagement = 4,
        SecurityCamera = 0,
        Radio = 0,
        TimeControl = 0,
    
        -- DISPATCH
        MovePlayerInRadio = 4,
        EnterRadioFreq = 0,
        SendRadioMessage = 0,
        AddNotesToAlert = 0,
        AssignAlertToUnit = 3,
    
        -- SEARCH CITIZEN
        SetWanted = 4,
        SetDanger = 0,
        CreateNotes = 0,
        PinNotes = 4,
        DeleteNotes = 4,
        CreateBill = 1,
        DeleteBill = 4,
        AddLicenses = 4,
        DeleteLicenses = 4,
    
        -- REPORTS
        CreateReport = 0,
        AddPeopleToReport = 0,
        AddBillReport = 0,
        RemovePeopleFromReport = 1,
        AddEvidence = 0,
        DeleteEvidence = 1,
        AddReportAgent = 0,
        AddTags = 0,
        RemoveTags = 1,
        AddVictimToReport = 0,
        AddVehicleToReport = 0,
        DeleteReport = 3,
    
        -- AddFederal
        AddFederal = 1,
    
        -- SecurityCameras
        SeeBusinessCameras = 3,
        SeeVehicleCamera = 1,
        SeeBodyCams = 1,
    
        -- PoliceManagement
        GenerateBadge = 4,
        AddPolice = 4,
        ChangePoliceGrade = 4,
        ChangePoliceBadge = 4,
        AddCondecorate = 4,
        RemoveCondecorate = 4,
        AddDivision = 4,
        RemoveDivision = 4,
        HirePolice = 4,

        -- Shapes
        Operations = 1,
        CreateShape = 1,
        DeleteShape = 1,
    
        -- RIGHT MENU
        RadialCommunicationTab = 1,
        RadioTab = 0,
        InteractTab = 1,
        HolsterTab = 1,
        ObjectPlacementTab = 1,
        CanTackle = 1,
    },
    ["lssd1"] = {
        --Tabs
        Dispatch = 0,
        SearchCitizen = 0,
        SearchReports = 0,
        SearchVehicles = 0,
        CriminalCode = 0,
        SearchCapture = 0,
        SearchDebtors = 0,
        FederalManagement = 0,
        AgentManagement = 4,
        SecurityCamera = 0,
        Radio = 0,
        TimeControl = 0,
    
        -- DISPATCH
        MovePlayerInRadio = 4,
        EnterRadioFreq = 0,
        SendRadioMessage = 0,
        AddNotesToAlert = 0,
        AssignAlertToUnit = 3,
    
        -- SEARCH CITIZEN
        SetWanted = 4,
        SetDanger = 0,
        CreateNotes = 0,
        PinNotes = 4,
        DeleteNotes = 4,
        CreateBill = 1,
        DeleteBill = 4,
        AddLicenses = 4,
        DeleteLicenses = 4,
    
        -- REPORTS
        CreateReport = 0,
        AddPeopleToReport = 0,
        AddBillReport = 0,
        RemovePeopleFromReport = 1,
        AddEvidence = 0,
        DeleteEvidence = 1,
        AddReportAgent = 0,
        AddTags = 0,
        RemoveTags = 1,
        AddVictimToReport = 0,
        AddVehicleToReport = 0,
        DeleteReport = 3,
    
        -- AddFederal
        AddFederal = 1,
    
        -- SecurityCameras
        SeeBusinessCameras = 3,
        SeeVehicleCamera = 1,
        SeeBodyCams = 1,
    
        -- PoliceManagement
        GenerateBadge = 4,
        AddPolice = 4,
        ChangePoliceGrade = 4,
        ChangePoliceBadge = 4,
        AddCondecorate = 4,
        RemoveCondecorate = 4,
        AddDivision = 4,
        RemoveDivision = 4,
        HirePolice = 4,

        -- Shapes
        Operations = 1,
        CreateShape = 1,
        DeleteShape = 1,
    
        -- RIGHT MENU
        RadialCommunicationTab = 1,
        RadioTab = 0,
        InteractTab = 1,
        HolsterTab = 1,
        ObjectPlacementTab = 1,
        CanTackle = 1,
    },
    ["ambulance"] = {
        --Tabs
        Dispatch = 0,
        SearchCitizen = 1,
        SearchReports = 1,
        SearchVehicles = 99,
        CriminalCode = 3,
        SearchCapture = 99,
        SearchDebtors = 99,
        FederalManagement = 99,
        AgentManagement = 3,
        Radio = 0,
        TimeControl = 1,
        
        -- DISPATCH
        MovePlayerInRadio = 2,
        EnterRadioFreq = 0,
        SendRadioMessage = 1,
        AddNotesToAlert = 1,
        AssignAlertToUnit = 3,
        
        -- SEARCH CITIZEN
        SetWanted = 4,
        SetDanger = 1,
        CreateNotes = 1,
        PinNotes = 4,
        DeleteNotes = 4,
        CreateBill = 1,
        DeleteBill = 4,
        AddLicenses = 99,
        DeleteLicenses = 99,
        
        -- REPORTS
        CreateReport = 1,
        AddPeopleToReport = 1,
        AddBillReport = 1,
        RemovePeopleFromReport = 1,
        AddEvidence = 1,
        DeleteEvidence = 1,
        AddReportAgent = 1,
        AddTags = 1,
        RemoveTags = 1,
        AddVictimToReport = 1,
        AddVehicleToReport = 1,
        DeleteReport = 3,
        
        -- AddFederal
        AddFederal = 99,
        
        -- SecurityCameras
        SeeBusinessCameras = 99,
        SeeVehicleCamera = 99,
        SeeBodyCams = 99,
        
        -- PoliceManagement
        GenerateBadge = 99,
        AddPolice = 99,
        ChangePoliceGrade = 99,
        ChangePoliceBadge = 99,
        AddCondecorate = 99,
        RemoveCondecorate = 99,
        AddDivision = 99,
        RemoveDivision = 99,
        HirePolice = 99,

        -- Shapes
        Operations = 99,
        CreateShape = 99,
        DeleteShape = 99,
        
        -- RIGHT MENU
        RadialCommunicationTab = 999,
        RadioTab = 0,
        InteractTab = 999,
        HolsterTab = 999,
        ObjectPlacementTab = 99999,
        CanTackle = 99,
    },
    ["sheriff"] = {
        --Tabs
        Dispatch = 1,
        SearchCitizen = 1,
        SearchReports = 1,
        SearchVehicles = 1,
        CriminalCode = 1,
        SearchCapture = 1,
        SearchDebtors = 1,
        FederalManagement = 1,
        AgentManagement = 4,
        SecurityCamera = 1,
        Radio = 1,
        TimeControl = 1,
    
        -- DISPATCH
        MovePlayerInRadio = 4,
        EnterRadioFreq = 1,
        SendRadioMessage = 1,
        AddNotesToAlert = 1,
        AssignAlertToUnit = 3,
    
        -- SEARCH CITIZEN
        SetWanted = 4,
        SetDanger = 1,
        CreateNotes = 1,
        PinNotes = 4,
        DeleteNotes = 4,
        CreateBill = 1,
        DeleteBill = 4,
        AddLicenses = 4,
        DeleteLicenses = 4,
    
        -- REPORTS
        CreateReport = 1,
        AddPeopleToReport = 1,
        AddBillReport = 1,
        RemovePeopleFromReport = 1,
        AddEvidence = 1,
        DeleteEvidence = 1,
        AddReportAgent = 1,
        AddTags = 1,
        RemoveTags = 1,
        AddVictimToReport = 1,
        AddVehicleToReport = 1,
        DeleteReport = 3,
    
        -- AddFederal
        AddFederal = 1,
    
        -- SecurityCameras
        SeeBusinessCameras = 3,
        SeeVehicleCamera = 1,
        SeeBodyCams = 1,
    
        -- PoliceManagement
        GenerateBadge = 4,
        AddPolice = 4,
        ChangePoliceGrade = 4,
        ChangePoliceBadge = 4,
        AddCondecorate = 4,
        RemoveCondecorate = 4,
        AddDivision = 4,
        RemoveDivision = 4,
        HirePolice = 4,

        -- Shapes
        Operations = 1,
        CreateShape = 1,
        DeleteShape = 1,
    
        -- RIGHT MENU
        RadialCommunicationTab = 1,
        RadioTab = 1,
        InteractTab = 1,
        HolsterTab = 1,
        ObjectPlacementTab = 1,
        CanTackle = 1,
    },
}

Config.PermissionsGroups = {
    "mod" -- list of groups that will have all permissions, like "mod", "admin", "superadmin", etc
}

exports("GerPermissions", function()
    return Config.Permissions
end)