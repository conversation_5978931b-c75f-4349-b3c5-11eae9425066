<!DOCTYPE html>
<html lang="es">
    <head>
        <title>origen_police</title>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="./lib/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
        <link href="https://cdn.lineicons.com/3.0/lineicons.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="./lib/animate.min.css"/>
        <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        
        <link href="./css/style.css" rel="stylesheet" >
        <link href="./css/style_menu.css" rel="stylesheet">
        <link href="./css/police.css" rel="stylesheet">
        <link href="./lib/dataTables.bootstrap5.min.css" rel="stylesheet">
        <script src="./lib/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
        <script src="./lib/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
        <link rel="stylesheet" href="./lib/swiper-bundle.min.css" />
        <script src="./lib/leaflet.js" integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ==" crossorigin=""></script>
        <link rel="stylesheet" href="./lib/leaflet.css" integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="crossorigin="" />
        <script src="./lib/leaflet.draw.js" integrity="sha512-ozq8xQKq6urvuU6jNgkfqAmT7jKN2XumbrX1JiB3TnF7tI48DPI4Gy1GXKD/V3EExgAs1V+pRO7vwtS1LHg0Gw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
        <link rel="stylesheet" href="./lib/leaflet.draw.css" integrity="sha512-gc3xjCmIy673V6MyOAZhIW93xhM9ei1I+gLbmFjUHIjocENRsLX/QUE1htk5q1XV2D/iie/VQ8DXI6Vu8bexvQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    </head>
    <body>
        <audio src="sounds/flash.ogg" id="flash"></audio>
        
        <div class="screen">
            <div class="apps"></div>
            <div class="notifications"></div>
        </div>

        <div class="cam-overlay">
            <img src="./img/rec.png" class="cam-recording animate__animated animate__flash animate__slow animate__infinite">
            <div class="cam-info">
                <div class="name"></div>
                <div class="other"></div>
            </div>
        </div>

        <div class="radar-flash"></div>
        <div class="block-informe">
            <div class="container-informe scale-in">
                <div class="informe">
                    <div class="logo-lspd">
                        <img src="./img/sapd_logo.png" height="65px"/>
                    </div>
                    <div class="content-informe">
                        <h1 translate="ForensicTitle">Forensic Analysis Report</h1>
                        <p class="text-justify" translate="ForensicDesc">Through this present report, the Scientific Department of the San Andreas Police shows the complete analysis of the attached evidence, the approximate time of the fact or in case of not knowing the time of collecting the evidence and/or analysis of this.</p>

                        <div class="zona-evidencias">


                        </div>

                        <div class="w-100 text-center">
                            <div class="black fecha-informe">In San Andreas, January 1, 2023</div>
                        </div>
                    </div>

                </div>
                <img src="" class="foto-informe">
            </div>
        </div>

        <div class="menu">
            <div class="col">
                <h5>Forward</h5>
                <div class="velocidad"><span id="speed-1">000</span> KM/h</div>
                <div class="col">
                    <h6>Plate</h6>
                    <p class="matricula" id="matricula-1">123456</p>
                </div>
                <div class="col">
                    <h6>Model</h6>
                    <p class="modelo" id="modelo-1">123456</p>
                </div>
            </div>
            <div class="col">
                <h5>Rear</h5>
                <div class="velocidad"><span id="speed-2">000</span> KM/h</div>
                <div class="col">
                    <h6>Plate</h6>
                    <p class="matricula" id="matricula-2">123456</p>
                </div>
                <div class="col">
                    <h6>Model</h6>
                    <p class="modelo" id="modelo-2">123456</p>
                </div>
            </div>
            <span class="block">Blocked</span>
        </div>

        <div class="federal">
            Remaining sentence: 5 months
        </div>

        <div class="sheriff_badge"></div>
        <div class="police_badge"></div>

        <div class="location">
            <span></span>
        </div>

        <div class="canvas"></div>

        <div class="nosignal"></div>

        <section class="friends">
            <div class="friends-bg"></div>
            <div class="friends-block">
                <div class="police-tab-list">
                    <!-- <div class="police-tab selected" tab="ref">
                        <i class="fa-solid fa-hashtag"></i>
                    </div>
                    <div class="police-tab" tab="com-rad">
                        <i class="fa-solid fa-circle-nodes"></i>

                    </div> -->
                    <div class="police-tab selected" tab="radio">
                        <i class="fa-solid fa-walkie-talkie"></i>

                    </div>
                    <div class="police-tab" tab="interaccion">
                        <i class="fas fa-user"></i>

                    </div>
                    <div class="police-tab" tab="guns">
                        <i class="fa-solid fa-gun"></i>

                    </div>
                    <div class="police-tab" tab="items">
                        <i class="fa-solid fa-road-barrier"></i>
                    </div>
                    <div class="police-tab" tab="settings">
                        <i class="fa-solid fa-wrench"></i>
                    </div>
                    <div class="d-flex flex-column align-items-center" style="width: 8.5vh;">
                        <small style="
                        color: #8b8b8b;
                        font-size: 1vh;
                    " translate="Availabel"></small>
                        <label class="switch">
                            <input type="checkbox" class="check-dispo" disabled>
                            <span class="slider round"></span>
                        </label>
                    </div>

                </div>
                <div class="tab-list">

                    <div class="tab-content referencias" tab="ref">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/ref.webp"><span translate="Reference">REFERENCES</span></h4>
                            </div>

                        </div>
                        <div class="ref-container">
                            <div class="title-1" translate="Icons">Icons</div>
                            <div class="ref-list">
                                <div class="ref active" blip="1">
                                    <img src="https://docs.fivem.net/blips/radar_level.png">
                                </div>
                            </div>
                        </div>
                        <div class="color-container">
                            <div class="title-1" translate="Colors">COLORS</div>
                            <div class="color-list">
                                <div class="color active" number="0" style="background-color:#FFFFFF"></div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-content com-rad" tab="com-rad">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/com.webp"><span translate="RadialComm">Radial communications</span></h4>
                            </div>

                        </div>
                        <div class="com-list">
                            <div class="row p-2 pt-0">

                            </div>
                        </div>
                    </div>
                    <div class="tab-content radio" tab="radio">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/walkie.webp"><span translate="Radio">RADIO</span></h4>
                            </div>
                            <div class="connected-zone">
                                <div class="freq-name" translate="Disconnected">DISCONNECTED</div>
                                <div class="disconnect-button">
                                    <i class="fa-solid fa-power-off"></i>
                                </div>
                            </div>
                        </div>
                        <div class="radio-list">

                        </div>
                    </div>
                    <div class="tab-content com-rad active" tab="interaccion">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/com.webp"><span translate="Interaction">INTERACTION</span></h4>
                            </div>

                        </div>
                        <div class="interaccion-list">
                            <div class="title-1" translate="CitizenInteraction">Citizen interaction</div>
                            <div class="citizen-interaction">
                                <div class="action-police" event="cachear"><i class="lni lni-user"></i> <div translate="Search">Cache</div></div>
                                <div class="action-police" event="esposar"><i class="lni lni-user"></i> <div translate="Wifes">Wifes</div></div>
                                <div class="action-police" event="escoltar"><i class="lni lni-user"></i> <div translate="Escort">To listen</div></div>
                                <div class="action-police" event="vehicleinof"><i class="lni lni-user"></i> <div translate="PutInVehicle">Put/take a vehicle</div></div>
                                <div class="action-police" event="placaje"><i class="lni lni-user"></i> <div translate="JumpTo">Plywood</div></div>
                                <div class="action-police" event="revive"><i class="lni lni-user"></i> <div translate="revive">Revive</div></div>
                                <div class="action-police" event="health"><i class="lni lni-user"></i> <div translate="HealWounds">To heal wounds</div></div>
                                <div class="action-police" event="anklecuff"><i class="lni lni-user"></i> <div translate="PutTakeAnkle">Put/take an ankle cuff</div></div>
                            </div>
                            <div class="title-1" translate="VehicleInteraction">Interacción con vehículos</div>
                            <div class="citizen-interaction" id="vehicle-interaction">
                                <div class="action-police" event="vehicledata"><i class="lni lni-car-alt"></i> <div translate="VehicleInformation">Vehicle information</div></div>
                                <div class="action-police" event="dvpolice"><i class="lni lni-car-alt"></i> <div translate="SeizeVehicle">Seizure vehicle</div></div>
                                <!-- <div class="action-police" event="callTow"><i class="lni lni-car-alt"></i> <div translate="CallTow">Call tow truck</div></div> -->
                                <div class="action-police" event="openveh"><i class="lni lni-car-alt"></i> <div translate="ForceLock">Force the lock</div></div>
                                <div class="action-police" event="stoptraffic"><i class="lni lni-car-alt"></i> <div translate="StopTraffic">Stop traffic</div></div>
                                <div class="action-police" event="slowtraffic"><i class="lni lni-car-alt"></i> <div translate="ReduceTraffic">Reduce traffic</div></div>
                                <div class="action-police" event="resumetraffic"><i class="lni lni-car-alt"></i> <div translate="ResumeTraffic">Resume traffic</div></div>
                            </div>

                        </div>
                    </div>
                    <div class="tab-content com-rad" tab="guns">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/settings.webp"><div translate="WeaponsConfiguration">Weapons configuration</div></h4>
                            </div>

                        </div>
                        <div class="interaccion-list">
                            <div class="citizen-interaction">
                                <div class="action-police" event="unarm"><i class="lni lni-user"></i> <div translate="ShowHideWeapons">Show/hide weapons</div></div>
                            </div>
                            <div class="title-1" translate="PistolPos">Pisto position</div>
                            <div class="citizen-interaction">
                                <div class="action-police" event="holster boxers"><i class="lni lni-user"></i> <div translate="Front">Front</div></div>
                                <div class="action-police" event="holster backhandgun"><i class="lni lni-user"></i> <div translate="Behind">Behind</div></div>
                                <div class="action-police" event="holster waisthandgun"><i class="lni lni-user"></i> <div translate="WaistCart">Waist cartridge</div></div>
                                <div class="action-police" event="holster handguns"><i class="lni lni-user"></i> <div translate="NormalCart">Normal cartridge</div></div>
                                <div class="action-police" event="holster chesthandgun"><i class="lni lni-user"></i> <div translate="ChestCart">Chest cartridge</div></div>
                                <div class="action-police" event="holster hiphandgun"><i class="lni lni-user"></i> <div translate="ThighCart">Thigh cartridge</div></div>
                                <div class="action-police" event="holster leghandgun"><i class="lni lni-user"></i> <div translate="LegCart">Leg cartridge</div></div>
                                <div class="action-police" event="holster handguns2"><i class="lni lni-user"></i> <div translate="SeparateLegCart">Separate leg cartridge</div></div>
                            </div>
                            <div class="title-1" translate="RiflePos">Rifles position</div>
                            <div class="citizen-interaction">
                                <div class="action-police" event="holster tacticalrifle"><i class="lni lni-car-alt"></i> <div translate="Chest">Chest</div></div>
                                <div class="action-police" event="holster assault"><i class="lni lni-car-alt"></i> <div translate="Back">Back</div></div>
                            </div>

                        </div>
                    </div>
                    <div class="tab-content com-rad" tab="items">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/road.webp" translate="PoliceObjects">Police objects</h4>
                            </div>

                        </div>
                        <div class="interaccion-list">
                            <div class="row p-2 pt-0">
                                <div class="col-4 p-1">
                                    <div class="com-item flex-column action-police" event="plceobj Conos">
                                        <img src="./img/icons/dY14HWl.png" class="com-img">
                                        <div class="com-title" translate="Cone">CONE</div>
                                    </div>
                                </div>
                                <div class="col-4 p-1">
                                    <div class="com-item flex-column action-police" event="plceobj Barreras">
                                        <img src="./img/icons/X3HiArg.png" class="com-img">
                                        <div class="com-title" translate="Barrier">Barrier</div>
                                    </div>
                                </div>
                                <div class="col-4 p-1">
                                    <div class="com-item flex-column action-police" event="plceobj Señales de tráfico">
                                        <img src="./img/icons/upRwW5B.png" class="com-img">
                                        <div class="com-title" translate="Sign">SIGN</div>
                                    </div>
                                </div>
                                <div class="col-4 p-1" id="spikesButton">
                                    <div class="com-item flex-column action-police" event="plceobj Pinchos">
                                        <img src="./img/icons/yXlqUwh.png" class="com-img">
                                        <div class="com-title" translate="Spikes">Spikes</div>
                                    </div>
                                </div>
                                <div class="col-4 p-1" id="radarButton">
                                    <div class="com-item flex-column action-police" event="plceobj Radar">
                                        <img src="./img/icons/6oQyhmQ.png" class="com-img">
                                        <div class="com-title" translate="Radar">Radar</div>
                                    </div>
                                </div>
                                <div class="col-4 p-1">
                                    <div class="com-item flex-column action-police bg-qrr" event="rmveobj">
                                        <img src="./img/icons/f15NAZT.png" class="com-img">
                                        <div class="com-title" translate="Delete">Delete</div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                    <div class="tab-content com-rad" tab="settings">
                        <div class="d-flex align-items-center tab-title justify-content-between">
                            <div>
                                <h4 class="gobold m-0"><img src="img/webp/settings.webp"><span translate="Settings">SETTINGS</span></h4>
                            </div>

                        </div>
                        <div class="settings-container">
                            <div class="title-1" translate="Settings">SETTINGS</div>
                            <div class="setting-list" setting="reference" style="opacity: 0.5;">
                                <div style="width: 100%;">
                                    <div class="config-item">
                                        <div translate="ReferencesLocation">References Location</div>
                                        <label class="switch">
                                            <input class="reference-location-check" type="checkbox" checked disabled>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="setting-list" setting="body-cam">
                                <div style="width: 100%;">
                                    <div class="config-item">
                                        <div translate="BodyCamera">Body Camera</div>
                                        <label class="switch">
                                            <input class="body-cam-check" type="checkbox" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row p-2 pt-0">
                                <div class="col-4 p-1 radio_anim" anim="1">
                                    <div class="com-item new-class-1 flex-column">
                                        <i class="fa-solid fa-walkie-talkie" style="font-size: 2.5vh;"></i> <!-- Cambia 'fa-icon-1' por el ícono que prefieras -->
                                        <div class="com-title" style="font-size: 1.9vh;"><div style="margin-right: 0.4vh;" translate="Animation">Animation</div> 1</div> <!-- Cambia el título según necesites -->
                                    </div>
                                </div>
                        
                                <!-- Nuevo botón 2 -->
                                <div class="col-4 p-1 radio_anim" anim="2">
                                    <div class="com-item new-class-2 flex-column">
                                        <i class="fa-solid fa-walkie-talkie" style="font-size: 2.5vh;"></i> <!-- Cambia 'fa-icon-2' por el ícono que prefieras -->
                                        <div class="com-title" style="font-size: 1.9vh;"><div style="margin-right: 0.4vh;" translate="Animation">Animation</div> 2</div> <!-- Cambia el título según necesites -->
                                    </div>
                                </div>
                        
                                <!-- Nuevo botón 3 -->
                                <div class="col-4 p-1 radio_anim" anim="3">
                                    <div class="com-item new-class-3 flex-column">
                                        <i class="fa-solid fa-walkie-talkie" style="font-size: 2.5vh;"></i> <!-- Cambia 'fa-icon-3' por el ícono que prefieras -->
                                        <div class="com-title" style="font-size: 1.9vh;"><div style="margin-right: 0.4vh;" translate="Animation">Animation</div> 3</div> <!-- Cambia el título según necesites -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

          <section class="dispatch">

            <div class="dispatch-block">
                <div class="police-tab-list">
                    <div class="police-tab selected w-50 tab-alerts" tab="alerts">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="police-tab w-50 tab-config" tab="config">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
                <div class="tab-list">
                    <div class="tab-content alertas active" tab="alerts">
                        <h4 class="gobold m-0 tab-title d-flex align-items-center justify-content-between">
                            <img src="img/webp/ref.webp" class="icon">
                            <div class="d-flex flex-column w-100">
                                <div translate="Emergencies">
                                Emergencies
                                </div>
                                <div class="radio-alert-status">
                                    <i class="fas fa-walkie-talkie"></i> <div translate="Disconnected">DISCONNECTED</div>
                                </div>
                            </div>
                            <div class="disponibilidad-alert">
                                <img src="img/dispo.svg" class="dispo">
                                <div class="no-dispo"></div>
                            </div>
                        </h4>
                        <div class="alerts-container">
                            <div class="alert-list">
                                <div class="text-muted p-2 w-100 text-center" style="font-size:1.3vh;" translate="NoAlertRecived">There are no alerts received</div>
                            </div>

                            <div class="alerts-control">
                                <div class="control">
                                    <div class="key left"></i><i class="fas fa-arrow-left"></i></div>
                                </div>
                                <div class="control d-flex align-items-center">
                                    <div class="key acudir"><div class="actual-alert">0</div>/<div class="total-alert">0</div></div>
                                </div>
                                <div class="control">
                                    <div class="key right"><i class="fas fa-arrow-right"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-content config" tab="config">
                        <h4 class="gobold m-0 tab-title d-flex align-items-center justify-content-between">
                            <img src="img/webp/ref.webp" class="icon">
                            <div class="d-flex flex-column w-100" translate="Settings">
                                Settings

                            </div>
                        </h4>
                        <div class="config-list">
                            <div class="config-item">
                                <div translate="General">General</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="GENERALES" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="config-item">
                                <div translate="AlertsCode">Alerts 488, 487, 487-V</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="48X" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="config-item">
                                <div translate="DrugTrafficking">Drug trafficking</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="DRUGS" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="config-item">
                                <div translate="VehicleRobs">Vehicle robberies</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="FORZAR" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="config-item">
                                <div translate="Alerts215">Alerts 215 / Weapons</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="215" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="config-item">
                                <div translate="Radars">Radars</div>
                                <label class="switch">
                                    <input type="checkbox" class="check-config-alerts" action="RADARES" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>

                            <div class="config-item">
                                <div translate="KeyToAlert">Key to go to the alert</div>
                                <button class="key-selector" action="origen_police:dispatch:mrkalert">
                                    U
                                </button>
                            </div>
                            <div class="config-item">
                                <div translate="DeleteAlertKey">Alert Delete Key</div>
                                <button class="key-selector" action="origen_police:dispatch:delalert">
                                    U
                                </button>
                            </div>
                            <div class="config-item">
                                <div translate="EmergencyOpenKey">Emergencies opening key</div>
                                <button class="key-selector" action="origen_police:dispatch:alertas">
                                    U
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        <div class="carmic">
            <i class="fas fa-bullhorn"></i>
        </div>

        <div class="stars">
            <img src="./img/icons/0FTFLQV.png">
            <img src="./img/icons/0FTFLQV.png">
            <img src="./img/icons/0FTFLQV.png">
            <img src="./img/icons/0FTFLQV.png">
            <img src="./img/icons/0FTFLQV.png">
        </div>
        
        <script src="./lib/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
        <script src="./lib/jquery-ui.min.js" type="text/javascript"></script>
        <script src="./lib/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
        <script src="./lib/jquery.dataTables.min.js"></script>
        <script src="./lib/dataTables.bootstrap5.min.js"></script>
        <script type="text/javascript" src="js/vendor/html2canvas.js"></script>
        <script type="text/javascript" src="js/config.js"></script>
        <script type="text/javascript" src="js/mapLogic.js"></script>
        <script type="text/javascript" src="js/app.js"></script>

        <!-- MANAGERS -->
        <script type="text/javascript" src="js/managers/audio.js"></script>
        <script type="text/javascript" src="js/managers/translations.js"></script>
        <!-- MANAGERS -->

        <script type="text/javascript" src="js/agents.js"></script>
        <script type="text/javascript" src="js/audio.js"></script>
        <script type="text/javascript" src="js/badge.js"></script>
        <script type="text/javascript" src="js/bills.js"></script>
        <script type="text/javascript" src="js/camera.js"></script>
        <script type="text/javascript" src="js/central.js"></script>
        <script type="text/javascript" src="js/citizen.js"></script>
        <script type="text/javascript" src="js/clock.js"></script>
        <script type="text/javascript" src="js/dispatch.js"></script>
        <script type="text/javascript" src="js/evidence.js"></script>
        <script type="text/javascript" src="js/penalcode.js"></script>
        <script type="text/javascript" src="js/police.js"></script>
        <script type="text/javascript" src="js/quickmenu.js"></script>
        <script type="text/javascript" src="js/radio.js"></script>
        <script type="text/javascript" src="js/reports.js"></script>
        <script type="text/javascript" src="js/vehicles.js"></script>
    </body>
</html>