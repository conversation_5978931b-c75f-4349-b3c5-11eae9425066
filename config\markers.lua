Config.Maps = { -- List of stations
    "custom",
}
-- 可用地图：
-- default-gta -> 是游戏中的默认警察局
-- mission-row -> gabz 任务行地图：https://www.youtube.com/watch?v=uptc79n5OBs
-- artex-mrpd -> Artex 任务行地图：https://www.youtube.com/watch?v=LmXNMchAUDg
-- breze-mrpd -> Breze 任务行地图：https://www.youtube.com/watch?v=nRILrJ241Gw
-- nopixel-mrpd -> NoPixel 任务行地图
-- custom -> 您必须将标记放置在自定义地图中
-- 如果您想将自定义地图标记添加到脚本中，请在 discord 服务器中打开一张票

Config.MarkersDraw = true -- 在地图上绘制标记

-- 请勿触摸下方；

MarkersList = {
    RequestVehicle = {
        sprite = 36,
        event = "origen_police:client:buyveh",
        text = "请求车辆",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    ModifyVehicle = {
        sprite = 36,
        event = "origen_police:client:modifyveh",
        text = "修改车辆",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    SaveVehicle = {
        sprite = 36,
        radius = 3,
        event = "origen_police:client:deletevehicle",
        text = "保存车辆",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    RequestBoat = {
        sprite = 35,
        event = "origen_police:client:boat",
        text = "请求船只",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    SaveBoat = {
        sprite = 35,
        radius = 3,
        event = "origen_police:client:deletevehicle",
        text = "保存船只",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    SaveHelicopter = {
        sprite = 34,
        radius = 3,
        event = "origen_police:client:deletevehicle",
        text = "保存直升机",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    RequestHelicopter = {
        sprite = 34,
        event = "origen_police:client:helicop",
        text = "请求直升机",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    DressingRoom = {
        sprite = 20,
        event = "origen_police:client:clothing",
        text = "进入更衣室",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    ["Inventory/Armoury"] = {
        sprite = 20,
        event = "origen_police:client:inventory",
        text = "进入库存/武器库",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    EvidenceReport = {
        sprite = 20,
        event = "origen_police:client:makereport",
        text = "撰写证据报告",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
}


PublicMarkerList = {
    Duty = {
        sprite = 20,
        event = "origen_police:client:enterOnDuty",
        text = "开始值班",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    Finger = {
        sprite = 20,
        event = "origen_police:client:domyfinguer",
        text = "在读卡器上按指纹",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    CriminalClothes = {
        sprite = 20,
        event = "origen_police:client:CriminalClothes",
        text = "更换衣服",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    Pertenences = {
        sprite = 20,
        event = "origen_police:client:pertenences",
        text = "存放您的物品",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
    ConfiscatedVehicles = {
        sprite = 36,
        event = "origen_police:client:openConfiscatedMenu",
        text = "被没收的车辆",
        rgba = {r = 0, g = 0, b = 0, a = 255}
    },
}




Tables = {
    Markers = {
        
    }
}

Public = {
    Markers = {},
    CriminalClothe = {
        ["qb-clothing"] = {
            ["male"] = {
                ["hat"] = { item = -1, texture = 0},
                ["t-shirt"] = {
                    item = 15, texture = 0
                },
                ["torso2"] = {
                    item = 31, texture = 0
                },
                ["pants"] = {
                    item = 5, texture = 7
                },
                ["shoes"] = {
                    item = 56, texture = 1
                },
                ["arms"] = {
                    item = 12, texture = 0
                },
            }
        },
        ["illenium-appearance"] = {
            ["male"] = {
                model = "mp_m_freemode_01",
                components = {
                    {texture = 0, component_id = 0, drawable = 0},
                    {texture = 0, component_id = 1, drawable = 0},
                    {texture = 0, component_id = 2, drawable = 0},
                    {texture = 0, component_id = 3, drawable = 5},
                    {texture = 2, component_id = 4, drawable = 27},
                    {texture = 0, component_id = 5, drawable = 0},
                    {texture = 0, component_id = 6, drawable = 8},
                    {texture = 0, component_id = 7, drawable = 0},
                    {texture = 0, component_id = 8, drawable = 15},
                    {texture = 0, component_id = 9, drawable = 0},
                    {texture = 0, component_id = 10, drawable = 0},
                    {texture = 0, component_id = 11, drawable = 5}
                },        
            }
        }
    },
    Radars = {},
    Blips = {
        {
            coords = vector4(72.6817, -397.2723, 38.3668, 347.6852),
            sprite = 60,
            color = 29,
            name = "警察局",
            size = 1.0
        },
        -- {
        --     coords = vector4(-440.7959, 6019.4160, 31.4901, 320.6172),vector3(457.18, -986.22, 31.34)
        --     sprite = 60,
        --     color = 29,
        --     name = "警察局",
        --     size = 0.8
        -- }
        
        --[[
        {
            coords = vector4(429.7668, -976.2729, 30.7057, 159.6318),
            sprite = 60,
            color = 29,
            name = "Police Station",
            size = 0.8
        },
        {
            coords = vector4(1811.3948, 3690.8940, 34.1778, 186.9994),
            sprite = 60,
            color = 60,
            name = "Police Station",
            size = 0.8
        },
        {
            coords = vector4(409.46, -1628.92, 29.29, 318.28),
            sprite = 237,
            color = 0,
            name = "Impound",
            size = 0.8
        }]]
    },
    TrafficZones = {}
}

BillsNPCPositions = {}
SpawnConfiscatedVehicles = {}

function Shallowcopy(orig)
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        copy = {}
        for orig_key, orig_value in pairs(orig) do
            copy[orig_key] = orig_value
        end
    else
        copy = orig
    end
    return copy
end

for mapIndex, mapName in pairs(Config.Maps) do
    Tables.Markers[mapIndex] = {}
    local mapData = exports["origen_police"]:LoadMarkers(mapName)
    for markerType, markersData in pairs(mapData) do
        for markerIndex, markerData in pairs(markersData) do
            local newMarkerData = MarkersList[markerType]
            local newMarkerDataCopy = Shallowcopy(newMarkerData)
            if not newMarkerData then
                if markerType == "BillsNPC" then 
                    BillsNPCPositions[#BillsNPCPositions + 1] = markerData.coords
                    goto skip_marker
                end
                if markerType == 'SpawnConfiscatedVehicles' then
                    SpawnConfiscatedVehicles[mapIndex] = markerData.coords
                    goto skip_marker
                end
                newMarkerData = PublicMarkerList[markerType]
                newMarkerDataCopy = Shallowcopy(newMarkerData)
                newMarkerDataCopy.coords = markerData.coords
                newMarkerDataCopy.spawn = markerData.spawn
                newMarkerDataCopy.station = mapIndex
                newMarkerDataCopy.stationName = mapName
                Public.Markers[#Public.Markers + 1] = newMarkerDataCopy
            else
                newMarkerDataCopy.coords = markerData.coords
                newMarkerDataCopy.spawn = markerData.spawn
                newMarkerDataCopy.station = mapIndex
                newMarkerDataCopy.stationName = mapName
                Tables.Markers[mapIndex][#Tables.Markers[mapIndex] + 1] = newMarkerDataCopy
            end
            ::skip_marker::
        end
    end
end

Public.CriminalClothe = Public.CriminalClothe[Config.Clothing == "fivem-appearance" and "illenium-appearance" or Config.Clothing]