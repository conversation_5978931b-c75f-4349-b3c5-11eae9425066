<style>
    .citizen-selector-container{
        border:.1vh solid rgba(255, 255, 255, 0);
        padding:1vh;
        border-radius:10px;
        cursor:pointer;
        background-color: #ffffff26;
        transition: var(--cubic) 0.5s all; 

    }

    .citizen-selector-container:hover{
        background-color:#ffffff36;

    }

    .citizen-selector-container.open{
        background-color:#ffffff36;
    }

    .citizen-selector-container .search-selector{
        visibility: hidden;
        opacity:0;
        display:none;
    }

    .citizen-selector-container.open .search-selector{
        visibility: visible;
        animation: fadeInDown 0.3s var(--cubic) forwards;
        display:block;
        
    }


    .selector-citizen{
        display: flex;
        width:100%;
        align-items:center;
    }
    .citizen-img{
        width:4vh;
        height:4vh;
        background-image:url("./img/temp-user.png");
        background-size:cover;
        border-radius:5px;
        margin-right:1vh;
    }

    .citizen-selector-name{
        font-family: '<PERSON><PERSON> Neue';
        font-size: 1.7vh;
        line-height: 1.7vh;
    }
    
    .citizen-id-selector{
        font-size:1vh;
        font-family:"Quicksand";
        font-weight:500;
        text-transform:uppercase;
    }

</style>
<div class="citizen-selector-container">
    <div class="citizen-selector-search search-selector">
        <div class="row m-titles search-box">
            <div class="col-10 p-0">
                <input type="text" placeholder="Buscar ciudadano..." class="search-input w-100 input-search-citizen-selector">
            </div>
            <div class="col-2 p-0">
                <div class="btn btn-search btn-search-citizen-selector w-100">
                    <i class="fas fa-search"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="citizen-list">
        <div class="selector-citizen">
            <div class="citizen-img"></div>
            <div>
                <div class="citizen-name">Nacho Vidal</div>
                <div class="citizen-id-selector">
                    BTC293243
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(".citizen-selector-container").click(function(){
        $(this).toggleClass("open");
    });

</script>
