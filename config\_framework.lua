Config = {}

Config.Framework = "auto" -- The name of the framework: auto | esx | qbcore | ...
Config.MySQLSystem = "oxmysql" -- icmysql, oxmysql
Config.DatabaseStructureCheck = true -- If you want the script to check the tables structure to check if you're missing some columns
Config.CustomNotify = false -- If you want to use your own notify system set this to true and edit the code in origen_police/custom/client/client.lua
Config.Language = "en" -- The language of the script, if you want to add your own language go to config/translations.lua and add a new table for your language
Config.autoSetItems = false -- To add items automaticly via exports ['qb-core']:AddItems
Config.VoiceSystem = "pma-voice" -- pma-voice, saltychat(IN DEVELOPMENT, ISSUES ARE EXPECTED, NO SUPPORT FOR THIS YET)
Config.fixQS = false -- false to use the framework metadata system only availabel for ESX 
Config.Inventory = "ox_inventory" -- origen_inventory | qb-inventory | new-qb-inventory | qs-inventory | ox_inventory | ls-inventory | codem-inventory | core_inventory
Config.OxLibMenu = true -- Use ox_lib context menu
Config.RecieveAlwaysAlerts = true -- if it is false that checks if there's players inside dispatch tablet and will recieve the alerts to manage and redirect to specific patrols, if it set to true all players on duty will recieve the alerts
Config.Debug = false -- Enable or disable debug mode, this include the prints that you are going to see in F8 and Server Consolle
Config.PoliceJobName = "police" -- You can change this to your police job name, some people use lspd, sheriff as job name
Config.Clothing = "illenium-appearance" -- illenium-appearance, qb-clothing, fivem-appearance, esx_skin, codem-appearance
Config.Phone = "qs-smartphone-pro" -- default, qs-smartphone, qs-smartphone-pro, lb-phone
Config.NeedRadioForDispatch = true -- True: only players with radio will have access to dispatch, False: all players will have access to dispatch
Config.EvidenceDrawDistance = 20.0 -- The distance that the evidence will be drawn
Config.ShootAlert = true -- Enable shoot alert
Config.ConfiscateSystem = true -- To enable or disable the confiscate system
Config.AutoSetCriminalClothe = true -- To enable or disable the auto set criminal clothes when player is in jail
Config.DisplayPlateOnVehicleAlerts = true -- To enable or disable the display of the plate in the vehicle alerts
Config.ShowCurrentStreet = true -- Show at the top of the screen the current street that the police is in
Config.ChangeMinimapSize = true -- Allows resizing of the minimap 
Config.CheckVersions = true -- Check if there's a new version of the script

-- DONT TOUCH ABOVE;

exports("GetConfig", function(key)
    return Config[key]
end)

function debuger(...)
    if Config.Debug then
        print ('[^5Origen Police^0]', ...)
    end
end

if Config.Framework == "auto" then
    Config.Framework = GetResourceState("qb-core") ~= "missing" and "qbcore" or "esx"
end

Config.Translations = Translations
Config.LogsTranslations = LogsTranslations
MySQL = {}